import { SiteConfig } from "./types.js";

export interface SiteInfo {
  type: string;
  name: string;
  config: SiteConfig;
}

export class SiteDetector {
  private siteConfigs: Map<string, SiteConfig>;

  constructor() {
    this.siteConfigs = new Map();
    this.initializeSiteConfigs();
  }

  private initializeSiteConfigs(): void {
    // Amazon configuration
    this.siteConfigs.set('amazon', {
      name: 'Amazon',
      baseUrl: 'https://www.amazon.com',
      searchPath: '/s',
      selectors: {
        searchBox: '#twotabsearchtextbox',
        searchButton: '#nav-search-submit-button',
        productContainer: '[data-component-type="s-search-result"]',
        productLink: 'h2 a',
        nextPageButton: 'a[aria-label="Go to next page"]',
      },
      features: {
        hasInfiniteScroll: false,
        hasPagination: true,
        requiresJavaScript: true,
        hasAntiBot: true,
        hasSearchSuggestions: true,
      },
      rateLimit: {
        requestsPerMinute: 10,
        delayBetweenRequests: 6000,
      },
    });

    // eBay configuration
    this.siteConfigs.set('ebay', {
      name: 'eBay',
      baseUrl: 'https://www.ebay.com',
      searchPath: '/sch/i.html',
      selectors: {
        searchBox: '#gh-ac',
        searchButton: '#gh-btn',
        productContainer: '.s-item',
        productLink: '.s-item__link',
        nextPageButton: 'a[aria-label="Next page"]',
      },
      features: {
        hasInfiniteScroll: false,
        hasPagination: true,
        requiresJavaScript: true,
        hasAntiBot: false,
        hasSearchSuggestions: true,
      },
      rateLimit: {
        requestsPerMinute: 15,
        delayBetweenRequests: 4000,
      },
    });

    // Walmart configuration
    this.siteConfigs.set('walmart', {
      name: 'Walmart',
      baseUrl: 'https://www.walmart.com',
      searchPath: '/search',
      selectors: {
        searchBox: '#global-search-input',
        searchButton: '[data-automation-id="global-search-submit"]',
        productContainer: '[data-testid="item-stack"]',
        productLink: 'a[data-testid="product-title"]',
        loadMoreButton: '[data-testid="load-more-button"]',
      },
      features: {
        hasInfiniteScroll: true,
        hasPagination: false,
        requiresJavaScript: true,
        hasAntiBot: true,
        hasSearchSuggestions: true,
      },
      rateLimit: {
        requestsPerMinute: 8,
        delayBetweenRequests: 7500,
      },
    });

    // Best Buy configuration
    this.siteConfigs.set('bestbuy', {
      name: 'Best Buy',
      baseUrl: 'https://www.bestbuy.com',
      searchPath: '/site/searchpage.jsp',
      selectors: {
        searchBox: '#gh-search-input',
        searchButton: '.header-search-button',
        productContainer: '.sku-item',
        productLink: '.sku-title a',
        nextPageButton: '.sr-pagination__next',
      },
      features: {
        hasInfiniteScroll: false,
        hasPagination: true,
        requiresJavaScript: true,
        hasAntiBot: false,
        hasSearchSuggestions: true,
      },
      rateLimit: {
        requestsPerMinute: 12,
        delayBetweenRequests: 5000,
      },
    });

    // Generic configuration (fallback)
    this.siteConfigs.set('generic', {
      name: 'Generic Ecommerce',
      baseUrl: '',
      searchPath: '/search',
      selectors: {
        searchBox: 'input[type="search"], input[name*="search"], input[placeholder*="search"]',
        searchButton: 'button[type="submit"], input[type="submit"], .search-button',
        productContainer: '.product, .item, [class*="product"], [class*="item"]',
        productLink: 'a[href*="product"], a[href*="item"]',
        nextPageButton: '.next, .pagination-next, [aria-label*="next"]',
      },
      features: {
        hasInfiniteScroll: false,
        hasPagination: true,
        requiresJavaScript: false,
        hasAntiBot: false,
        hasSearchSuggestions: false,
      },
      rateLimit: {
        requestsPerMinute: 5,
        delayBetweenRequests: 12000,
      },
    });
  }

  detectSite(url: string): SiteInfo {
    const hostname = new URL(url).hostname.toLowerCase();
    
    // Check for known sites
    for (const [type, config] of this.siteConfigs.entries()) {
      if (type === 'generic') continue; // Skip generic for now
      
      if (this.matchesSite(hostname, type)) {
        return {
          type,
          name: config.name,
          config,
        };
      }
    }

    // Return generic configuration as fallback
    const genericConfig = this.siteConfigs.get('generic')!;
    return {
      type: 'generic',
      name: 'Unknown Ecommerce Site',
      config: {
        ...genericConfig,
        baseUrl: `${new URL(url).protocol}//${hostname}`,
      },
    };
  }

  private matchesSite(hostname: string, siteType: string): boolean {
    const patterns: Record<string, string[]> = {
      amazon: ['amazon.com', 'amazon.co.uk', 'amazon.de', 'amazon.fr', 'amazon.ca', 'amazon.in'],
      ebay: ['ebay.com', 'ebay.co.uk', 'ebay.de', 'ebay.fr', 'ebay.ca'],
      walmart: ['walmart.com'],
      bestbuy: ['bestbuy.com'],
    };

    const sitePatterns = patterns[siteType];
    if (!sitePatterns) return false;

    return sitePatterns.some(pattern => hostname.includes(pattern));
  }

  getSiteConfig(siteType: string): SiteConfig | undefined {
    return this.siteConfigs.get(siteType);
  }

  getAllSiteTypes(): string[] {
    return Array.from(this.siteConfigs.keys()).filter(type => type !== 'generic');
  }

  addCustomSite(type: string, config: SiteConfig): void {
    this.siteConfigs.set(type, config);
  }
}
