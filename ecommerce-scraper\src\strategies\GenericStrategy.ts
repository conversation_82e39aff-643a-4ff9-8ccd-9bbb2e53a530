import { BaseStrategy } from "./BaseStrategy.js";
import { Product } from "../types.js";

export class GenericStrategy extends BaseStrategy {
  name = "generic";

  canHandle(url: string): boolean {
    // Generic strategy can handle any URL as a fallback
    return true;
  }

  protected async performSearch(query: string): Promise<void> {
    try {
      // Generic search approach using AI
      console.log("Using generic strategy for search...");
      
      // Try to find and use search functionality
      await this.page.act(`Find the search box on this page and type "${query}"`);
      await this.page.waitForTimeout(1000);
      await this.page.act("Submit the search or click the search button");
      
      // Wait for results
      await this.page.waitForLoadState('networkidle', { timeout: 15000 });
      
    } catch (error) {
      console.error("Generic search failed:", error);
      throw error;
    }
  }

  protected async extractProductsFromPage(): Promise<Product[]> {
    try {
      // Use AI to extract products from any ecommerce site
      const extractionResult = await this.page.extract({
        instruction: `This appears to be an ecommerce website. Extract all product information visible on this page.
        Look for:
        - Product names/titles
        - Prices (current and original if on sale)
        - Product images
        - Product links/URLs
        - Ratings or reviews if available
        - Brand names
        - Product descriptions
        - Availability status
        - Any other relevant product information
        
        Return an array of all products found on this page.`,
        schema: {
          type: "object",
          properties: {
            products: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  name: { type: "string" },
                  price: { type: "string" },
                  originalPrice: { type: "string" },
                  rating: { type: "number" },
                  reviewCount: { type: "number" },
                  productUrl: { type: "string" },
                  imageUrl: { type: "string" },
                  brand: { type: "string" },
                  description: { type: "string" },
                  availability: { type: "string" }
                },
                required: ["name"]
              }
            }
          },
          required: ["products"]
        }
      });

      const products: Product[] = extractionResult.products.map((item: any) => ({
        name: item.name,
        price: item.price || "Price not available",
        originalPrice: item.originalPrice,
        currency: "USD", // Default currency
        rating: item.rating,
        reviewCount: item.reviewCount,
        imageUrl: item.imageUrl,
        productUrl: this.normalizeUrl(item.productUrl),
        brand: item.brand || "Unknown",
        description: item.description,
        availability: item.availability,
        category: "Unknown",
      }));

      console.log(`Extracted ${products.length} products using generic strategy`);
      return products;

    } catch (error) {
      console.error("Failed to extract products with generic strategy:", error);
      return await this.fallbackExtraction();
    }
  }

  private async fallbackExtraction(): Promise<Product[]> {
    try {
      console.log("Using basic fallback extraction...");
      
      // Try common ecommerce selectors
      const productSelectors = [
        '.product',
        '.item',
        '[class*="product"]',
        '[class*="item"]',
        '[data-testid*="product"]',
        '.card',
        '.listing',
      ];

      let productElements: any[] = [];
      
      for (const selector of productSelectors) {
        productElements = await this.page.locator(selector).all();
        if (productElements.length > 0) {
          console.log(`Found ${productElements.length} products with selector: ${selector}`);
          break;
        }
      }

      if (productElements.length === 0) {
        console.log("No products found with common selectors");
        return [];
      }

      const products: Product[] = [];

      for (let i = 0; i < Math.min(productElements.length, 20); i++) {
        try {
          const element = productElements[i];
          
          // Try to extract basic info using common patterns
          const nameSelectors = ['h1', 'h2', 'h3', '.title', '.name', '[class*="title"]', '[class*="name"]'];
          const priceSelectors = ['.price', '[class*="price"]', '[data-testid*="price"]'];
          const linkSelectors = ['a', '[href]'];
          const imageSelectors = ['img'];

          let name = '';
          let price = '';
          let productUrl = '';
          let imageUrl = '';

          // Extract name
          for (const selector of nameSelectors) {
            try {
              const nameElement = element.locator(selector).first();
              const text = await nameElement.textContent({ timeout: 1000 });
              if (text && text.trim().length > 0) {
                name = text.trim();
                break;
              }
            } catch (e) {
              continue;
            }
          }

          // Extract price
          for (const selector of priceSelectors) {
            try {
              const priceElement = element.locator(selector).first();
              const text = await priceElement.textContent({ timeout: 1000 });
              if (text && text.trim().length > 0) {
                price = text.trim();
                break;
              }
            } catch (e) {
              continue;
            }
          }

          // Extract URL
          for (const selector of linkSelectors) {
            try {
              const linkElement = element.locator(selector).first();
              const href = await linkElement.getAttribute('href', { timeout: 1000 });
              if (href) {
                productUrl = this.normalizeUrl(href);
                break;
              }
            } catch (e) {
              continue;
            }
          }

          // Extract image
          try {
            const imageElement = element.locator('img').first();
            const src = await imageElement.getAttribute('src', { timeout: 1000 });
            if (src) {
              imageUrl = this.normalizeUrl(src);
            }
          } catch (e) {
            // Image extraction is optional
          }

          if (name) {
            products.push({
              name,
              price: price || "Price not available",
              currency: "USD",
              productUrl,
              imageUrl,
              brand: "Unknown",
              category: "Unknown",
            });
          }

        } catch (error) {
          console.error(`Error extracting product ${i}:`, error);
        }
      }

      return products;
    } catch (error) {
      console.error("Fallback extraction failed:", error);
      return [];
    }
  }

  private normalizeUrl(url: string): string {
    if (!url) return '';
    
    // Handle relative URLs
    if (url.startsWith('/')) {
      const currentUrl = this.page.url();
      const baseUrl = new URL(currentUrl).origin;
      return baseUrl + url;
    }
    
    // Handle protocol-relative URLs
    if (url.startsWith('//')) {
      return 'https:' + url;
    }
    
    return url;
  }

  async handlePagination(): Promise<boolean> {
    try {
      // Use AI to find pagination
      await this.page.act("Look for and click the next page button or load more products button");
      await this.page.waitForLoadState('networkidle', { timeout: 10000 });
      return true;
    } catch (error) {
      // Fallback to parent implementation
      return await super.handlePagination();
    }
  }

  async detectAntiBot(): Promise<boolean> {
    try {
      // Generic anti-bot detection
      const pageText = await this.page.textContent('body');
      const suspiciousTexts = [
        'captcha',
        'robot',
        'blocked',
        'access denied',
        'suspicious activity',
        'verify you are human',
      ];

      for (const text of suspiciousTexts) {
        if (pageText?.toLowerCase().includes(text)) {
          return true;
        }
      }

      return await super.detectAntiBot();
    } catch (error) {
      return false;
    }
  }
}
