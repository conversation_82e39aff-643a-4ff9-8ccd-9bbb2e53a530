import { EcommerceAgent } from "../src/EcommerceAgent.js";
import { ScrapingOptions, Product } from "../src/types.js";
import StagehandConfig from "../stagehand.config.js";
import fs from 'fs/promises';

/**
 * Advanced usage examples for the Multi-Site Ecommerce Scraping Agent
 */

async function advancedPriceComparison() {
  console.log("🔍 Advanced Price Comparison Example");
  
  const agent = new EcommerceAgent(StagehandConfig, {
    headless: true,
    enableStealth: true,
  });

  try {
    await agent.initialize();

    const searchQuery = "iPhone 15";
    const sites = [
      "https://www.amazon.com",
      "https://www.ebay.com",
    ];

    const options: ScrapingOptions = {
      maxProducts: 10,
      maxPages: 2,
      priceRange: { min: 500, max: 1500 },
      sortBy: 'price_low',
    };

    console.log(`Searching for "${searchQuery}" across ${sites.length} sites...`);
    const results = await agent.searchMultipleSites(searchQuery, sites, options);

    // Combine and analyze results
    const allProducts: Product[] = [];
    results.forEach(result => {
      if (result.success) {
        allProducts.push(...result.products.map(p => ({
          ...p,
          source: result.metadata.site,
        })));
      }
    });

    // Sort by price
    allProducts.sort((a, b) => {
      const priceA = parseFloat(a.price.replace(/[^\d.]/g, ''));
      const priceB = parseFloat(b.price.replace(/[^\d.]/g, ''));
      return priceA - priceB;
    });

    console.log("\n📊 Price Comparison Results:");
    console.log("=" * 50);

    allProducts.slice(0, 10).forEach((product, index) => {
      console.log(`${index + 1}. ${product.name}`);
      console.log(`   Price: ${product.price}`);
      console.log(`   Source: ${product.source}`);
      console.log(`   Rating: ${product.rating || 'N/A'}`);
      console.log(`   URL: ${product.productUrl}`);
      console.log("");
    });

    // Export to CSV
    await exportToCSV(allProducts, `price-comparison-${Date.now()}.csv`);

  } catch (error) {
    console.error("Error in price comparison:", error);
  } finally {
    await agent.close();
  }
}

async function categoryBasedScraping() {
  console.log("📱 Category-Based Scraping Example");
  
  const agent = new EcommerceAgent(StagehandConfig, {
    headless: true,
    enableStealth: true,
  });

  try {
    await agent.initialize();

    const categories = [
      { query: "gaming laptop", category: "Electronics" },
      { query: "running shoes", category: "Sports" },
      { query: "coffee maker", category: "Home & Kitchen" },
    ];

    const results = {};

    for (const { query, category } of categories) {
      console.log(`\nScraping ${category}: "${query}"`);
      
      const categoryResults = await agent.searchMultipleSites(
        query,
        ["https://www.amazon.com"],
        {
          maxProducts: 5,
          maxPages: 1,
          minRating: 4.0,
        }
      );

      results[category] = categoryResults;
    }

    // Generate category report
    console.log("\n📋 Category Report:");
    Object.entries(results).forEach(([category, categoryResults]) => {
      console.log(`\n${category}:`);
      categoryResults.forEach(result => {
        console.log(`  ${result.metadata.site}: ${result.products.length} products`);
      });
    });

    // Export results
    await fs.writeFile(
      `category-report-${Date.now()}.json`,
      JSON.stringify(results, null, 2)
    );

  } catch (error) {
    console.error("Error in category scraping:", error);
  } finally {
    await agent.close();
  }
}

async function monitorPriceChanges() {
  console.log("📈 Price Monitoring Example");
  
  const agent = new EcommerceAgent(StagehandConfig, {
    headless: true,
    enableStealth: true,
  });

  try {
    await agent.initialize();

    const productsToMonitor = [
      { query: "MacBook Pro M3", targetPrice: 1800 },
      { query: "Sony WH-1000XM5", targetPrice: 300 },
    ];

    for (const { query, targetPrice } of productsToMonitor) {
      console.log(`\nMonitoring: ${query} (target: $${targetPrice})`);
      
      const results = await agent.searchMultipleSites(
        query,
        ["https://www.amazon.com", "https://www.ebay.com"],
        { maxProducts: 3, maxPages: 1 }
      );

      const deals = [];
      results.forEach(result => {
        if (result.success) {
          result.products.forEach(product => {
            const price = parseFloat(product.price.replace(/[^\d.]/g, ''));
            if (price <= targetPrice) {
              deals.push({
                ...product,
                source: result.metadata.site,
                savings: targetPrice - price,
              });
            }
          });
        }
      });

      if (deals.length > 0) {
        console.log(`🎉 Found ${deals.length} deals!`);
        deals.forEach(deal => {
          console.log(`  ${deal.name}: ${deal.price} (Save $${deal.savings.toFixed(2)})`);
          console.log(`  Source: ${deal.source}`);
        });
      } else {
        console.log("No deals found at target price");
      }
    }

  } catch (error) {
    console.error("Error in price monitoring:", error);
  } finally {
    await agent.close();
  }
}

async function exportToCSV(products: Product[], filename: string) {
  const headers = [
    'Name',
    'Price',
    'Original Price',
    'Rating',
    'Review Count',
    'Brand',
    'Availability',
    'Source',
    'URL'
  ];

  const csvContent = [
    headers.join(','),
    ...products.map(product => [
      `"${product.name?.replace(/"/g, '""') || ''}"`,
      `"${product.price || ''}"`,
      `"${product.originalPrice || ''}"`,
      product.rating || '',
      product.reviewCount || '',
      `"${product.brand || ''}"`,
      `"${product.availability || ''}"`,
      `"${(product as any).source || ''}"`,
      `"${product.productUrl || ''}"`,
    ].join(','))
  ].join('\n');

  await fs.writeFile(filename, csvContent);
  console.log(`📄 Results exported to ${filename}`);
}

// Export functions for use in other modules
export {
  advancedPriceComparison,
  categoryBasedScraping,
  monitorPriceChanges,
  exportToCSV,
};

// Run examples if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  console.log("Running advanced examples...");
  
  (async () => {
    await advancedPriceComparison();
    await categoryBasedScraping();
    await monitorPriceChanges();
  })().catch(console.error);
}
