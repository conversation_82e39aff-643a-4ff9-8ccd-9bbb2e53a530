import { Page } from "@browserbasehq/stagehand";
import { 
  Product, 
  SearchResults, 
  ScrapingOptions, 
  SiteStrategy,
  ProductSchema,
  SearchResultsSchema 
} from "../types.js";

export abstract class BaseStrategy implements SiteStrategy {
  protected page!: Page; // Will be initialized by agent
  protected maxRetries: number = 3;
  protected retryDelay: number = 2000;

  constructor(protected agent: any) {
    // Page will be set when agent is initialized
  }

  async initialize(page: Page): Promise<void> {
    this.page = page;
  }

  abstract name: string;
  abstract canHandle(url: string): boolean;

  async search(query: string, options: ScrapingOptions = {}): Promise<SearchResults> {
    const maxProducts = options.maxProducts || 20;
    const maxPages = options.maxPages || 3;
    
    let allProducts: Product[] = [];
    let currentPage = 1;
    let hasNextPage = true;

    try {
      // Perform initial search
      await this.performSearch(query);
      
      while (hasNextPage && currentPage <= maxPages && allProducts.length < maxProducts) {
        console.log(`Scraping page ${currentPage}...`);
        
        // Extract products from current page
        const pageProducts = await this.extractProductsFromPage();
        
        // Filter products based on options
        const filteredProducts = this.filterProducts(pageProducts, options);
        allProducts.push(...filteredProducts);
        
        // Check if we have enough products
        if (allProducts.length >= maxProducts) {
          allProducts = allProducts.slice(0, maxProducts);
          break;
        }
        
        // Try to go to next page
        hasNextPage = await this.handlePagination();
        if (hasNextPage) {
          currentPage++;
          await this.page.waitForTimeout(2000); // Delay between pages
        }
      }

      return SearchResultsSchema.parse({
        products: allProducts,
        totalResults: allProducts.length,
        currentPage,
        hasNextPage,
      });

    } catch (error) {
      console.error(`Search failed for query "${query}":`, error);
      throw error;
    }
  }

  protected abstract performSearch(query: string): Promise<void>;
  protected abstract extractProductsFromPage(): Promise<Product[]>;

  async handlePagination(): Promise<boolean> {
    try {
      // Look for next page button using multiple strategies
      const nextSelectors = [
        'a[aria-label*="next" i]',
        'a[aria-label*="Next" i]',
        '.next',
        '.pagination-next',
        '[data-testid*="next"]',
        'a:has-text("Next")',
        'button:has-text("Next")',
      ];

      for (const selector of nextSelectors) {
        try {
          const nextButton = this.page.locator(selector).first();
          if (await nextButton.isVisible({ timeout: 2000 })) {
            await nextButton.click();
            await this.page.waitForLoadState('networkidle', { timeout: 10000 });
            return true;
          }
        } catch (e) {
          // Continue to next selector
        }
      }

      // Try using Stagehand's AI to find next button
      try {
        await this.page.act("click the next page button");
        await this.page.waitForLoadState('networkidle', { timeout: 10000 });
        return true;
      } catch (e) {
        console.log("No next page found");
        return false;
      }

    } catch (error) {
      console.error("Pagination failed:", error);
      return false;
    }
  }

  async detectAntiBot(): Promise<boolean> {
    try {
      // Check for common anti-bot indicators
      const antibotSelectors = [
        'iframe[src*="captcha"]',
        'div[class*="captcha"]',
        'img[src*="captcha"]',
        '[data-testid*="captcha"]',
        '.cf-browser-verification',
        '#challenge-form',
        '.anti-bot',
        '.robot-check',
      ];

      for (const selector of antibotSelectors) {
        const element = this.page.locator(selector);
        if (await element.count() > 0) {
          return true;
        }
      }

      // Check for suspicious redirects or error pages
      const currentUrl = this.page.url();
      if (currentUrl.includes('captcha') || 
          currentUrl.includes('blocked') || 
          currentUrl.includes('access-denied')) {
        return true;
      }

      return false;
    } catch (error) {
      console.error("Anti-bot detection failed:", error);
      return false;
    }
  }

  async extractProduct(productUrl: string): Promise<Product> {
    await this.page.goto(productUrl);
    
    // Use Stagehand's extract method with AI
    const productData = await this.page.extract({
      instruction: `Extract all product information from this page including:
        - Product name/title
        - Current price and original price if on sale
        - Rating and number of reviews
        - Product description
        - Brand name
        - Availability status
        - Product images
        - Key features and specifications`,
      schema: ProductSchema,
    });

    return {
      ...productData,
      productUrl,
    };
  }

  protected filterProducts(products: Product[], options: ScrapingOptions): Product[] {
    let filtered = products;

    // Filter by price range
    if (options.priceRange) {
      filtered = filtered.filter(product => {
        const price = this.parsePrice(product.price);
        return price >= options.priceRange!.min && price <= options.priceRange!.max;
      });
    }

    // Filter by minimum rating
    if (options.minRating && options.minRating > 0) {
      filtered = filtered.filter(product => 
        product.rating && product.rating >= options.minRating!
      );
    }

    // Filter out of stock if requested
    if (!options.includeOutOfStock) {
      filtered = filtered.filter(product => 
        !product.availability || 
        !product.availability.toLowerCase().includes('out of stock')
      );
    }

    return filtered;
  }

  protected parsePrice(priceString: string): number {
    // Remove currency symbols and extract numeric value
    const cleaned = priceString.replace(/[^\d.,]/g, '');
    const price = parseFloat(cleaned.replace(',', ''));
    return isNaN(price) ? 0 : price;
  }

  protected async retryOperation<T>(
    operation: () => Promise<T>, 
    maxRetries: number = this.maxRetries
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        console.log(`Attempt ${attempt} failed:`, lastError.message);
        
        if (attempt < maxRetries) {
          await this.page.waitForTimeout(this.retryDelay * attempt);
        }
      }
    }
    
    throw lastError!;
  }

  protected async waitForElement(selector: string, timeout: number = 10000): Promise<boolean> {
    try {
      await this.page.waitForSelector(selector, { timeout });
      return true;
    } catch (error) {
      return false;
    }
  }
}
