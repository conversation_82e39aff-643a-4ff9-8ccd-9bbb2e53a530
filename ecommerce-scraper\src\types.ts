import { z } from "zod";

// Product data schema - standardized across all sites
export const ProductSchema = z.object({
  name: z.string(),
  price: z.string(),
  originalPrice: z.string().optional(),
  currency: z.string().default("USD"),
  rating: z.number().optional(),
  reviewCount: z.number().optional(),
  imageUrl: z.string().optional(),
  productUrl: z.string(),
  description: z.string().optional(),
  availability: z.string().optional(),
  brand: z.string().optional(),
  category: z.string().optional(),
  sku: z.string().optional(),
  features: z.array(z.string()).optional(),
  specifications: z.record(z.string()).optional(),
});

export type Product = z.infer<typeof ProductSchema>;

// Search results schema
export const SearchResultsSchema = z.object({
  products: z.array(ProductSchema),
  totalResults: z.number().optional(),
  currentPage: z.number().optional(),
  totalPages: z.number().optional(),
  hasNextPage: z.boolean().optional(),
});

export type SearchResults = z.infer<typeof SearchResultsSchema>;

// Site configuration interface
export interface SiteConfig {
  name: string;
  baseUrl: string;
  searchPath: string;
  selectors: {
    searchBox?: string;
    searchButton?: string;
    productContainer?: string;
    productLink?: string;
    nextPageButton?: string;
    loadMoreButton?: string;
  };
  features: {
    hasInfiniteScroll: boolean;
    hasPagination: boolean;
    requiresJavaScript: boolean;
    hasAntiBot: boolean;
    hasSearchSuggestions: boolean;
  };
  rateLimit: {
    requestsPerMinute: number;
    delayBetweenRequests: number;
  };
}

// Scraping options
export interface ScrapingOptions {
  maxProducts?: number;
  maxPages?: number;
  includeOutOfStock?: boolean;
  minRating?: number;
  priceRange?: {
    min: number;
    max: number;
  };
  sortBy?: 'relevance' | 'price_low' | 'price_high' | 'rating' | 'newest';
  filters?: Record<string, string>;
  timeout?: number;
  retries?: number;
}

// Agent execution result
export interface ScrapingResult {
  success: boolean;
  products: Product[];
  metadata: {
    site: string;
    searchQuery: string;
    totalFound: number;
    pagesScraped: number;
    timeElapsed: number;
    errors: string[];
  };
}

// Site strategy interface
export interface SiteStrategy {
  name: string;
  canHandle(url: string): boolean;
  search(query: string, options?: ScrapingOptions): Promise<SearchResults>;
  extractProduct(productUrl: string): Promise<Product>;
  handlePagination(): Promise<boolean>;
  detectAntiBot(): Promise<boolean>;
}

// Agent configuration
export interface AgentConfig {
  headless?: boolean;
  timeout?: number;
  retries?: number;
  userAgent?: string;
  viewport?: {
    width: number;
    height: number;
  };
  proxy?: {
    server: string;
    username?: string;
    password?: string;
  };
  enableStealth?: boolean;
  enableCache?: boolean;
}
