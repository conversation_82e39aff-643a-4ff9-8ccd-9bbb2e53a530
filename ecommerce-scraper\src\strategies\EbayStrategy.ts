import { BaseStrategy } from "./BaseStrategy.js";
import { Product } from "../types.js";

export class EbayStrategy extends BaseStrategy {
  name = "ebay";

  canHandle(url: string): boolean {
    return url.includes('ebay.com') || url.includes('ebay.');
  }

  protected async performSearch(query: string): Promise<void> {
    try {
      // eBay search implementation
      await this.page.act(`Type "${query}" into the search box`);
      await this.page.act("Click the search button");
      
      // Wait for results
      await this.page.waitForLoadState('networkidle', { timeout: 15000 });
      
    } catch (error) {
      console.error("eBay search failed:", error);
      throw error;
    }
  }

  protected async extractProductsFromPage(): Promise<Product[]> {
    try {
      const extractionResult = await this.page.extract({
        instruction: `Extract all product information from the eBay search results.
        For each product listing, get:
        - Product title/name
        - Current price (including shipping if shown)
        - Buy It Now vs Auction format
        - Seller rating and feedback score
        - Number of watchers or sold items
        - Product condition (New, Used, Refurbished, etc.)
        - Product URL
        - Product image
        - Shipping information
        - Time left for auctions
        
        Focus on the main search results, ignore sponsored listings.`,
        schema: {
          type: "object",
          properties: {
            products: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  name: { type: "string" },
                  price: { type: "string" },
                  condition: { type: "string" },
                  format: { type: "string" },
                  shipping: { type: "string" },
                  productUrl: { type: "string" },
                  imageUrl: { type: "string" },
                  sellerRating: { type: "string" },
                  watchers: { type: "number" },
                  timeLeft: { type: "string" }
                },
                required: ["name", "price", "productUrl"]
              }
            }
          },
          required: ["products"]
        }
      });

      const products: Product[] = extractionResult.products.map((item: any) => ({
        name: item.name,
        price: item.price,
        currency: "USD",
        imageUrl: item.imageUrl,
        productUrl: this.normalizeEbayUrl(item.productUrl),
        availability: item.condition || "Unknown",
        description: `${item.format || ''} ${item.condition || ''}`.trim(),
        features: [
          item.format,
          item.shipping,
          item.timeLeft,
          item.sellerRating
        ].filter(Boolean),
      }));

      console.log(`Extracted ${products.length} products from eBay`);
      return products;

    } catch (error) {
      console.error("Failed to extract eBay products:", error);
      return await this.fallbackExtraction();
    }
  }

  private async fallbackExtraction(): Promise<Product[]> {
    try {
      console.log("Using fallback extraction for eBay...");
      
      const productElements = await this.page.locator('.s-item').all();
      const products: Product[] = [];

      for (let i = 0; i < Math.min(productElements.length, 20); i++) {
        try {
          const element = productElements[i];
          
          const nameElement = element.locator('.s-item__title').first();
          const priceElement = element.locator('.s-item__price').first();
          const linkElement = element.locator('.s-item__link').first();
          const imageElement = element.locator('.s-item__image img').first();
          const conditionElement = element.locator('.s-item__subtitle').first();

          const name = await nameElement.textContent() || '';
          const price = await priceElement.textContent() || '';
          const href = await linkElement.getAttribute('href') || '';
          const imageUrl = await imageElement.getAttribute('src') || '';
          const condition = await conditionElement.textContent() || '';

          if (name && price && href) {
            products.push({
              name: name.trim(),
              price: price.trim(),
              currency: "USD",
              imageUrl,
              productUrl: href,
              availability: condition.trim(),
              brand: "Unknown",
              category: "Unknown",
            });
          }
        } catch (error) {
          console.error(`Error extracting eBay product ${i}:`, error);
        }
      }

      return products;
    } catch (error) {
      console.error("eBay fallback extraction failed:", error);
      return [];
    }
  }

  private normalizeEbayUrl(url: string): string {
    if (!url) return '';
    
    // eBay URLs are usually already complete
    if (url.startsWith('http')) {
      return url;
    }
    
    return 'https://www.ebay.com' + url;
  }

  async handlePagination(): Promise<boolean> {
    try {
      // eBay-specific pagination
      const nextButton = this.page.locator('a[aria-label="Next page"]').first();
      
      if (await nextButton.isVisible({ timeout: 3000 })) {
        await nextButton.click();
        await this.page.waitForLoadState('networkidle', { timeout: 15000 });
        return true;
      }

      return await super.handlePagination();
    } catch (error) {
      console.error("eBay pagination failed:", error);
      return false;
    }
  }
}
