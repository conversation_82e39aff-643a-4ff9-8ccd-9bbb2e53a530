import { Stagehand, <PERSON>, BrowserContext } from "@browserbasehq/stagehand";
import {
  ScrapingOptions,
  ScrapingR<PERSON>ult,
  AgentConfig,
  SiteStrategy
} from "./types.js";
import { SiteDetector } from "./SiteDetector.js";
import { AmazonStrategy } from "./strategies/AmazonStrategy.js";
import { EbayStrategy } from "./strategies/EbayStrategy.js";
import { GenericStrategy } from "./strategies/GenericStrategy.js";

export class EcommerceAgent {
  private stagehand: Stagehand;
  private page!: Page; // Will be initialized in initialize()
  private context!: BrowserContext; // Will be initialized in initialize()
  private siteDetector: SiteDetector;
  private strategies: Map<string, SiteStrategy>;
  private config: AgentConfig;

  constructor(stagehandConfig: any, agentConfig: AgentConfig = {}) {
    this.config = {
      headless: true,
      timeout: 30000,
      retries: 3,
      enableStealth: true,
      enableCache: true,
      ...agentConfig
    };

    this.stagehand = new Stagehand({
      ...stagehandConfig,
      headless: this.config.headless,
    });

    this.siteDetector = new SiteDetector();
    this.strategies = new Map();
    this.initializeStrategies();
  }

  private initializeStrategies(): void {
    const strategies = [
      new AmazonStrategy(this),
      new EbayStrategy(this),
      new GenericStrategy(this), // Fallback strategy
    ];

    strategies.forEach(strategy => {
      this.strategies.set(strategy.name, strategy);
    });
  }

  async initialize(): Promise<void> {
    await this.stagehand.init();
    this.page = this.stagehand.page;
    this.context = this.stagehand.context;

    // Initialize all strategies with the page
    for (const strategy of this.strategies.values()) {
      if ('initialize' in strategy && typeof strategy.initialize === 'function') {
        await strategy.initialize(this.page);
      }
    }

    // Configure stealth mode if enabled
    if (this.config.enableStealth) {
      await this.enableStealthMode();
    }

    // Set viewport if specified
    if (this.config.viewport) {
      await this.page.setViewportSize(this.config.viewport);
    }

    // Set user agent if specified
    if (this.config.userAgent) {
      await this.page.setExtraHTTPHeaders({
        'User-Agent': this.config.userAgent
      });
    }
  }

  private async enableStealthMode(): Promise<void> {
    // Add stealth configurations
    await this.page.addInitScript(() => {
      // Remove webdriver property
      Object.defineProperty(navigator, 'webdriver', {
        get: () => undefined,
      });

      // Mock plugins
      Object.defineProperty(navigator, 'plugins', {
        get: () => [1, 2, 3, 4, 5],
      });

      // Mock languages
      Object.defineProperty(navigator, 'languages', {
        get: () => ['en-US', 'en'],
      });
    });
  }

  async searchProducts(
    query: string, 
    siteUrl: string, 
    options: ScrapingOptions = {}
  ): Promise<ScrapingResult> {
    const startTime = Date.now();
    const result: ScrapingResult = {
      success: false,
      products: [],
      metadata: {
        site: siteUrl,
        searchQuery: query,
        totalFound: 0,
        pagesScraped: 0,
        timeElapsed: 0,
        errors: []
      }
    };

    try {
      // Detect site and get appropriate strategy
      const siteInfo = this.siteDetector.detectSite(siteUrl);
      const strategy = this.getStrategy(siteInfo.type);

      if (!strategy) {
        throw new Error(`No strategy found for site: ${siteInfo.type}`);
      }

      // Navigate to the site
      await this.page.goto(siteUrl, { 
        waitUntil: 'networkidle',
        timeout: this.config.timeout 
      });

      // Check for anti-bot detection
      const hasAntiBot = await strategy.detectAntiBot();
      if (hasAntiBot) {
        await this.handleAntiBot();
      }

      // Perform search
      const searchResults = await strategy.search(query, options);
      
      result.success = true;
      result.products = searchResults.products;
      result.metadata.totalFound = searchResults.totalResults || searchResults.products.length;
      result.metadata.pagesScraped = searchResults.currentPage || 1;

    } catch (error) {
      result.metadata.errors.push(error instanceof Error ? error.message : String(error));
      console.error(`Error scraping ${siteUrl}:`, error);
    } finally {
      result.metadata.timeElapsed = Date.now() - startTime;
    }

    return result;
  }

  async searchMultipleSites(
    query: string, 
    siteUrls: string[], 
    options: ScrapingOptions = {}
  ): Promise<ScrapingResult[]> {
    const results: ScrapingResult[] = [];

    for (const siteUrl of siteUrls) {
      try {
        console.log(`Searching ${siteUrl} for "${query}"...`);
        const result = await this.searchProducts(query, siteUrl, options);
        results.push(result);

        // Add delay between sites to avoid rate limiting
        if (siteUrls.indexOf(siteUrl) < siteUrls.length - 1) {
          await this.page.waitForTimeout(2000);
        }
      } catch (error) {
        console.error(`Failed to search ${siteUrl}:`, error);
        results.push({
          success: false,
          products: [],
          metadata: {
            site: siteUrl,
            searchQuery: query,
            totalFound: 0,
            pagesScraped: 0,
            timeElapsed: 0,
            errors: [error instanceof Error ? error.message : String(error)]
          }
        });
      }
    }

    return results;
  }

  private getStrategy(siteType: string): SiteStrategy | undefined {
    return this.strategies.get(siteType) || this.strategies.get('generic');
  }

  private async handleAntiBot(): Promise<void> {
    console.log("Anti-bot detection detected, implementing countermeasures...");
    
    // Add random delays
    await this.page.waitForTimeout(Math.random() * 3000 + 2000);
    
    // Simulate human-like mouse movements
    await this.page.mouse.move(
      Math.random() * 800, 
      Math.random() * 600
    );
    
    // Check for CAPTCHA or other challenges
    const captchaElements = await this.page.locator('iframe[src*="captcha"], div[class*="captcha"], img[src*="captcha"]').count();
    if (captchaElements > 0) {
      console.warn("CAPTCHA detected - manual intervention may be required");
      await this.page.waitForTimeout(5000);
    }
  }

  async getPage(): Promise<Page> {
    return this.page;
  }

  async getContext(): Promise<BrowserContext> {
    return this.context;
  }

  async close(): Promise<void> {
    await this.stagehand.close();
  }

  // Utility method for debugging
  async takeScreenshot(filename?: string): Promise<void> {
    const name = filename || `screenshot-${Date.now()}.png`;
    await this.page.screenshot({ path: name, fullPage: true });
    console.log(`Screenshot saved: ${name}`);
  }
}
