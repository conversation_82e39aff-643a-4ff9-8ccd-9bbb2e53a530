import { EcommerceAgent } from "../src/EcommerceAgent.js";
import { ScrapingOptions } from "../src/types.js";
import StagehandConfig from "../stagehand.config.js";

/**
 * Basic usage example for the Multi-Site Ecommerce Scraping Agent
 */

async function basicExample() {
  // Initialize the agent
  const agent = new EcommerceAgent(StagehandConfig, {
    headless: true, // Run in headless mode for production
    timeout: 30000,
    enableStealth: true,
  });

  try {
    await agent.initialize();
    console.log("Agent initialized successfully");

    // Example 1: Search a single site
    console.log("\n=== Single Site Search ===");
    const singleSiteResult = await agent.searchProducts(
      "laptop", 
      "https://www.amazon.com",
      {
        maxProducts: 5,
        maxPages: 1,
        minRating: 4.0,
      }
    );

    console.log(`Found ${singleSiteResult.products.length} laptops on Amazon`);
    singleSiteResult.products.forEach((product, index) => {
      console.log(`${index + 1}. ${product.name} - ${product.price}`);
    });

    // Example 2: Search multiple sites
    console.log("\n=== Multi-Site Search ===");
    const multiSiteResults = await agent.searchMultipleSites(
      "smartphone",
      [
        "https://www.amazon.com",
        "https://www.ebay.com",
      ],
      {
        maxProducts: 3,
        maxPages: 1,
      }
    );

    multiSiteResults.forEach(result => {
      console.log(`\n${result.metadata.site}: ${result.products.length} products`);
      if (result.success) {
        result.products.forEach(product => {
          console.log(`  - ${product.name}: ${product.price}`);
        });
      } else {
        console.log(`  Error: ${result.metadata.errors.join(", ")}`);
      }
    });

  } catch (error) {
    console.error("Error:", error);
  } finally {
    await agent.close();
  }
}

// Run the example
if (import.meta.url === `file://${process.argv[1]}`) {
  basicExample().catch(console.error);
}

export { basicExample };
