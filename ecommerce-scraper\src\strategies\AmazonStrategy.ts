import { BaseStrategy } from "./BaseStrategy.js";
import { Product, ScrapingOptions } from "../types.js";

export class AmazonStrategy extends BaseStrategy {
  name = "amazon";

  canHandle(url: string): boolean {
    return url.includes('amazon.com') || url.includes('amazon.');
  }

  protected async performSearch(query: string): Promise<void> {
    try {
      // Wait for search box and perform search
      await this.page.act(`Type "${query}" into the search box`);
      await this.page.act("Click the search button");
      
      // Wait for results to load
      await this.page.waitForLoadState('networkidle', { timeout: 15000 });
      
      // Handle any popups or overlays
      await this.dismissPopups();
      
    } catch (error) {
      console.error("Amazon search failed:", error);
      throw error;
    }
  }

  protected async extractProductsFromPage(): Promise<Product[]> {
    try {
      // Use Stagehand's AI extraction for Amazon products
      const extractionResult = await this.page.extract({
        instruction: `Extract all product information from the Amazon search results. 
        For each product, get:
        - Product name/title
        - Current price (look for price-whole, price-fraction, or a-price classes)
        - Original price if on sale (look for text-decoration: line-through)
        - Rating (look for star ratings, usually out of 5)
        - Number of reviews (look for review count numbers)
        - Product URL (the link to the product page)
        - Product image URL
        - Prime shipping availability
        - Sponsored/ad indicators
        
        Focus on the main search results, ignore sponsored ads at the top.
        Return an array of products with all available information.`,
        schema: {
          type: "object",
          properties: {
            products: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  name: { type: "string" },
                  price: { type: "string" },
                  originalPrice: { type: "string" },
                  rating: { type: "number" },
                  reviewCount: { type: "number" },
                  productUrl: { type: "string" },
                  imageUrl: { type: "string" },
                  isPrime: { type: "boolean" },
                  isSponsored: { type: "boolean" },
                  availability: { type: "string" }
                },
                required: ["name", "price", "productUrl"]
              }
            }
          },
          required: ["products"]
        }
      });

      // Process and standardize the extracted data
      const products: Product[] = extractionResult.products.map((item: any) => ({
        name: item.name,
        price: item.price,
        originalPrice: item.originalPrice,
        currency: "USD",
        rating: item.rating,
        reviewCount: item.reviewCount,
        imageUrl: item.imageUrl,
        productUrl: this.normalizeAmazonUrl(item.productUrl),
        availability: item.availability,
        brand: this.extractBrandFromTitle(item.name),
        category: "Unknown",
        features: item.isPrime ? ["Prime Shipping"] : undefined,
      }));

      console.log(`Extracted ${products.length} products from Amazon`);
      return products;

    } catch (error) {
      console.error("Failed to extract Amazon products:", error);
      
      // Fallback: try to extract using basic selectors
      return await this.fallbackExtraction();
    }
  }

  private async fallbackExtraction(): Promise<Product[]> {
    try {
      console.log("Using fallback extraction for Amazon...");
      
      // Get all product containers
      const productElements = await this.page.locator('[data-component-type="s-search-result"]').all();
      const products: Product[] = [];

      for (let i = 0; i < Math.min(productElements.length, 20); i++) {
        try {
          const element = productElements[i];
          
          // Extract basic product info using selectors
          const nameElement = element.locator('h2 a span').first();
          const priceElement = element.locator('.a-price-whole, .a-price .a-offscreen').first();
          const linkElement = element.locator('h2 a').first();
          const ratingElement = element.locator('[aria-label*="out of 5 stars"]').first();
          const imageElement = element.locator('img').first();

          const name = await nameElement.textContent() || '';
          const price = await priceElement.textContent() || '';
          const href = await linkElement.getAttribute('href') || '';
          const ratingText = await ratingElement.getAttribute('aria-label') || '';
          const imageUrl = await imageElement.getAttribute('src') || '';

          if (name && price && href) {
            products.push({
              name: name.trim(),
              price: price.trim(),
              currency: "USD",
              rating: this.parseRating(ratingText),
              imageUrl,
              productUrl: this.normalizeAmazonUrl(href),
              brand: this.extractBrandFromTitle(name),
              category: "Unknown",
            });
          }
        } catch (error) {
          console.error(`Error extracting product ${i}:`, error);
        }
      }

      return products;
    } catch (error) {
      console.error("Fallback extraction failed:", error);
      return [];
    }
  }

  private normalizeAmazonUrl(url: string): string {
    if (!url) return '';
    
    // Handle relative URLs
    if (url.startsWith('/')) {
      url = 'https://www.amazon.com' + url;
    }
    
    // Clean up Amazon URLs (remove tracking parameters)
    try {
      const urlObj = new URL(url);
      const cleanPath = urlObj.pathname.split('/ref=')[0]; // Remove ref tracking
      return `${urlObj.origin}${cleanPath}`;
    } catch (error) {
      return url;
    }
  }

  private extractBrandFromTitle(title: string): string {
    // Try to extract brand from common Amazon title patterns
    const brandPatterns = [
      /^([A-Z][a-zA-Z0-9\s&]+?)\s+[-–]/,  // Brand - Product
      /^([A-Z][a-zA-Z0-9\s&]+?)\s+\w+/,   // Brand Product
    ];

    for (const pattern of brandPatterns) {
      const match = title.match(pattern);
      if (match && match[1].length < 30) {
        return match[1].trim();
      }
    }

    return "Unknown";
  }

  private parseRating(ratingText: string): number | undefined {
    const match = ratingText.match(/(\d+\.?\d*)\s+out of/);
    return match ? parseFloat(match[1]) : undefined;
  }

  private async dismissPopups(): Promise<void> {
    try {
      // Common Amazon popup dismissal
      const popupSelectors = [
        '[aria-label="Close"]',
        '.a-button-close',
        '#nav-flyout-searchAjax .nav-flyout-close',
        '.a-popover-close',
      ];

      for (const selector of popupSelectors) {
        try {
          const element = this.page.locator(selector);
          if (await element.isVisible({ timeout: 1000 })) {
            await element.click();
            await this.page.waitForTimeout(500);
          }
        } catch (e) {
          // Continue to next selector
        }
      }
    } catch (error) {
      // Ignore popup dismissal errors
    }
  }

  async handlePagination(): Promise<boolean> {
    try {
      // Amazon-specific pagination
      const nextButton = this.page.locator('a[aria-label="Go to next page"]').first();
      
      if (await nextButton.isVisible({ timeout: 3000 })) {
        await nextButton.click();
        await this.page.waitForLoadState('networkidle', { timeout: 15000 });
        return true;
      }

      // Fallback to generic pagination
      return await super.handlePagination();
    } catch (error) {
      console.error("Amazon pagination failed:", error);
      return false;
    }
  }

  async detectAntiBot(): Promise<boolean> {
    try {
      // Amazon-specific anti-bot detection
      const amazonAntiBot = [
        'img[src*="captcha"]',
        '.a-box-group:has-text("Enter the characters")',
        '#captchacharacters',
        '.cvf-widget-form',
      ];

      for (const selector of amazonAntiBot) {
        if (await this.page.locator(selector).count() > 0) {
          return true;
        }
      }

      // Check for "Sorry, we just need to make sure you're not a robot"
      const robotText = await this.page.locator('text="robot"').count();
      if (robotText > 0) {
        return true;
      }

      return await super.detectAntiBot();
    } catch (error) {
      return false;
    }
  }
}
